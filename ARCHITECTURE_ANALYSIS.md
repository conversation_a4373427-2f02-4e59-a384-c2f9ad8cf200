# Instickerly - Architecture Analysis

## Project Overview
Instickerly is a Flutter photo editing application focused on fonts, stickers, and templates. The app follows a modular, scalable architecture with clean separation of concerns.

## Core Technologies
- **Flutter 3.27.3** with Dart SDK >=3.6.0
- **State Management**: flutter_bloc (Cubit pattern)
- **Dependency Injection**: get_it with custom DependencyProvider wrapper
- **Navigation**: go_router with shell routes and nested navigation
- **Local Database**: Isar for local data persistence
- **Code Generation**: freezed, build_runner, go_router_builder
- **Analytics**: Firebase Analytics, PostHog, AppsFlyer

## Project Structure

### Workspace Organization
```
instickerly/
├── features/                    # Feature modules (business logic)
│   ├── fonts_feature/          # Font editing functionality
│   ├── stickers_feature/       # Sticker management
│   ├── templates_feature/      # Template system
│   ├── main_shell_feature/     # Main app shell & navigation
│   ├── settings_feature/       # App settings
│   ├── purchases_feature/      # In-app purchases
│   ├── rate_us_feature/        # Rating system
│   ├── whats_new_feature/      # What's new screen
│   └── notifications_feature/  # Push notifications
├── packages/                   # Shared packages
│   ├── common/                 # Utilities, localization, observers
│   ├── core/                   # Business logic, services, DI
│   ├── router/                 # Navigation logic & route generation
│   └── ui_kit/                 # Design system, UI components
├── mobile/                     # Main Flutter application
├── tools/                      # Development scripts
└── forks/                      # Forked dependencies
```

## Clean Architecture Implementation

### Three-Layer Architecture
Each feature follows clean architecture with strict layer separation:

#### 1. Data Layer (`src/data/`)
- **Repositories**: Implementation of domain interfaces
- **Data Sources**: Local (Isar) and Remote (Firebase) data sources
- **Models**: Data transfer objects and mappers
- **Dependency Injection**: Data layer DI registration

#### 2. Domain Layer (`src/domain/`)
- **Entities**: Business objects
- **Repository Interfaces**: Abstract contracts for data access
- **Interactors/Use Cases**: Business logic implementation
- **Dependency Injection**: Domain layer DI registration

#### 3. UI Layer (`src/ui/`)
- **Pages**: Screen implementations
- **Widgets**: Reusable UI components
- **State Management**: Cubits for state management
- **Analytics**: Feature-specific analytics tracking
- **Dependency Injection**: UI layer DI registration

### Example Feature Structure (Stickers Feature)
```
stickers_feature/
├── lib/
│   └── src/
│       ├── data/
│       │   ├── database_source/
│       │   │   ├── local_source/
│       │   │   └── remote_source/
│       │   ├── repository/
│       │   └── di/
│       ├── domain/
│       │   ├── interactor/
│       │   └── di/
│       └── ui/
│           ├── stickers/
│           ├── favorite_stickers/
│           ├── sticker_packs/
│           └── di/
```

## Dependency Injection Architecture

### DependencyProvider Wrapper
- Wraps get_it with custom methods for registration and retrieval
- Supports factories, lazy singletons, and parameterized dependencies
- Environment-specific configurations

### Registration Pattern
```dart
// Data Layer
DependencyProvider.registerFactory<Repository>(
  () => RepositoryImpl(
    localSource: DependencyProvider.get<LocalSource>(),
    remoteSource: DependencyProvider.get<RemoteSource>(),
  ),
);

// Domain Layer
DependencyProvider.registerFactory<Interactor>(
  () => InteractorImpl(
    repository: DependencyProvider.get<Repository>(),
  ),
);

// UI Layer
DependencyProvider.registerFactory<Cubit>(
  () => CubitImpl(
    interactor: DependencyProvider.get<Interactor>(),
    analytics: DependencyProvider.get<Analytics>(),
  ),
);
```

## Navigation Architecture

### Go Router with Shell Routes
- **Main Shell**: Bottom navigation with 3 tabs (Home, Favorites, Settings)
- **Feature Shells**: Nested navigation for fonts/stickers/templates
- **Route Generation**: Type-safe routes with go_router_builder

### Navigation Structure
```
BottomNavigationShell
├── HomeShell (Tab 0)
│   ├── FontsTab
│   ├── StickersTab
│   └── TemplatesTab
├── FavoritesShell (Tab 1)
│   ├── FavoriteFonts
│   ├── FavoriteStickers
│   └── FavoriteTemplates
└── SettingsShell (Tab 2)
```

### Analytics Integration
- **Route Observers**: Track all navigation events
- **Multiple Providers**: Firebase, PostHog, AppsFlyer
- **Screen Tracking**: Automatic screen view tracking
- **Custom Events**: Feature-specific analytics

## State Management

### Flutter Bloc (Cubit Pattern)
- **Cubits**: Simplified state management without events
- **Freezed States**: Immutable state classes with code generation
- **BlocProvider**: Dependency injection for cubits
- **BlocBuilder/Listener**: UI state consumption

### State Structure Example
```dart
@freezed
class FeatureState with _$FeatureState {
  const factory FeatureState.loading() = _LoadingState;
  const factory FeatureState.loaded({
    required List<Item> items,
    required Status status,
  }) = _LoadedState;
  const factory FeatureState.error({required String message}) = _ErrorState;
}
```

## UI Kit & Design System

### AppTheme Structure
- **AppColorScheme**: Comprehensive color palette (gray warm/cold, yellow, red)
- **AppTextStyles**: Typography system with variable fonts
- **AppIcons**: SVG icon system with generated assets
- **AppGradients**: Gradient definitions

### Component Library
- **Buttons**: MainButton, SimpleButton, TxtButton, LoadingButton
- **Inputs**: Custom text fields with actions
- **Overlays**: Modals, dropdowns, sliders
- **Layout**: Unfocuser, SystemOverlayStyle, transitions

### Asset Management
- **Flutter Gen**: Type-safe asset access
- **Organized Structure**: SVG/PNG assets by feature
- **Custom Fonts**: Extensive font library for editing features

## Firebase Integration

### Services Used
- **Firebase Core**: Base configuration
- **Analytics**: User behavior tracking
- **Crashlytics**: Error reporting
- **Remote Config**: Feature flags and A/B testing
- **Storage**: File storage for stickers/templates
- **Firestore**: Document database
- **Messaging**: Push notifications
- **App Check**: Security verification

## Key Architectural Patterns

### Repository Pattern
- Abstract interfaces in domain layer
- Concrete implementations in data layer
- Multiple data sources (local/remote) coordination

### Interactor Pattern
- Business logic encapsulation
- Single responsibility use cases
- Domain layer orchestration

### Observer Pattern
- Navigation tracking
- Analytics event propagation
- State change notifications

### Factory Pattern
- Dependency creation
- Analytics tracker instantiation
- Route generation

## Development Workflow

### Code Generation
- **Freezed**: Immutable classes and unions
- **Go Router**: Type-safe route generation
- **Flutter Gen**: Asset generation
- **Build Runner**: Coordinated code generation

### Environment Management
- **Multiple Environments**: dev, pre-prod, prod
- **Configuration Files**: JSON-based environment configs
- **Firebase Projects**: Separate projects per environment

This architecture provides a scalable, maintainable foundation for the photo editing application with clear separation of concerns and comprehensive tooling support.
