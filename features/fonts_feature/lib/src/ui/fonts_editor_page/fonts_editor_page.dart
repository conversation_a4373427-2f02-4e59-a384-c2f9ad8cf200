import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/common.dart';
import 'package:core/core.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/state/fonts_editor_cubit.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/app_bar/app_bar_image.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/font_case_selection/font_case_selection_tab.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/history_buttons.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/select_alignment/select_alignment_tab.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/select_blur/select_blur_tab.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/select_fonts/select_fonts.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/select_shadow/select_shadow_tab.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/select_spacing/select_spacing_tab.dart';
import 'package:router/router.dart';
import 'package:ui_kit/ui_kit.dart';
import 'dart:ui' as dart;

part 'widgets/app_bar/app_bar.dart';
part 'widgets/bottom_sheet/bottom_sheet_wrapper.dart';
part 'widgets/editor_bottom_bar.dart';
part 'widgets/subtype_selector_and_delete_button.dart';

class FontsEditorPage extends StatelessWidget {
  final CustomFontModel selectedCustomFont;
  final bool isFromFavorites;

  const FontsEditorPage({
    required this.selectedCustomFont,
    required this.isFromFavorites,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<FontsEditorCubit>(
      create: (context) => DependencyProvider.get<FontsEditorCubit>()..init(selectedCustomFont, isFromFavorites),
      child: const _Body(),
    );
  }
}

class _Body extends StatefulWidget {
  const _Body();

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> with SnackbarMixin {
  final _focusNode = FocusNode();
  final _controller = TextEditingController();
  final GlobalKey _textFormFieldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<FontsEditorCubit>();

    return SystemOverlayStyleComponent.lightOverlay(
      color: context.colors.grayCold9,
      child: BlocConsumer<FontsEditorCubit, FontsEditorState>(
        buildWhen: (previous, current) => current.map(
          empty: (_) => true,
          snackbar: (_) => false,
          initial: (state) => true,
        ),
        listenWhen: (_, current) => current.map(
          empty: (_) => false,
          snackbar: (_) => true,
          initial: (state) => true,
        ),
        listener: (context, state) {
          state.mapOrNull(
            initial: (state) {
              if (state.textField.text != _controller.text) {
                _controller.text = state.textField.text;
              }
            },
            snackbar: (FontsEditorSnackbarState value) {
              return switch (value.type) {
                SnackBarType.fontDownloadSuccess => showSuccessSnackbar(LocaleKeys.fontDownloaded.tr()),
                SnackBarType.fontDownloadFailed =>
                  showErrorSnackbar(LocaleKeys.errorOccurred.tr(), message: LocaleKeys.fontNotDownloaded.tr()),
                SnackBarType.copyToClipboardSuccess => showSuccessSnackbar(LocaleKeys.copiedToClipboard.tr()),
                SnackBarType.copyToClipboardFailed =>
                  showErrorSnackbar(LocaleKeys.errorOccurred.tr(), message: LocaleKeys.fontNotCopiedToClipboard.tr()),
              };
            },
          );
        },
        builder: (context, state) {
          return state.maybeMap(
            initial: (state) {
              return Scaffold(
                backgroundColor: context.colors.grayCold9,
                appBar: _AppBar(
                  focusNode: _focusNode,
                  onCopyToClipboardClick: () async {
                    if (state.textField.currentFontModel.isPro && !context.isPurchaseActive) {
                      cubit.onCopyToClipboardClickButUserNotSubscribed();
                      const PurchasesRoute().push(context);
                    } else {
                      _focusNode.unfocus();
                      cubit.onCopyToClipboardClick(_textFormFieldKey);
                    }
                  },
                  onDownloadClick: () async {
                    if (state.textField.currentFontModel.isPro && !context.isPurchaseActive) {
                      cubit.onDownloadClickButUserNotSubscribed();
                      const PurchasesRoute().push(context);
                    } else {
                      _focusNode.unfocus();
                      cubit.onDownloadClick(_textFormFieldKey);
                    }
                  },
                  textIsNotEmpty: state.textField.text.isNotEmpty,
                ),
                body: Stack(
                  children: [
                    const EditorBackground(),
                    Positioned.fill(
                      bottom: 77,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          final width = constraints.maxWidth;
                          final height = constraints.maxHeight - 77;

                          return _Editor(
                            focusNode: _focusNode,
                            maxWidth: width,
                            maxHeight: height,
                            state: state,
                            shouldCentered: state.bottomBarSelectedIndex != null,
                            controller: _controller,
                            textFormFieldKey: _textFormFieldKey,
                          );
                        },
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _SubtypeSelectorAndLanguageSelector(
                            onDeleteClick: cubit.onDeleteClick,
                            onFontSubtypeChanged: cubit.onFontSubtypeChanged,
                            font: state.textField.currentFontModel,
                            showMenu: state.bottomBarSelectedIndex == 0,
                            onLanguageTypeSelect: cubit.onLanguageTypeSelect,
                            fontsSupportType: state.fontsSupportType,
                          ),
                          const SizedBox(height: 8),
                          _BottomSheetWrapper(state: state),
                        ],
                      ),
                    ),
                  ],
                ),
                bottomNavigationBar: _EditorBottomBar(
                  currentIndex: state.bottomBarSelectedIndex,
                  onTap: cubit.onBottomBarItemClick,
                ),
              );
            },
            orElse: () => const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }
}

class _Editor extends StatefulWidget {
  final FocusNode focusNode;
  final double maxWidth;
  final double maxHeight;
  final FontsEditorInitialState state;
  final bool shouldCentered;
  final TextEditingController controller;
  final GlobalKey textFormFieldKey;

  const _Editor({
    required this.focusNode,
    required this.maxWidth,
    required this.maxHeight,
    required this.state,
    required this.shouldCentered,
    required this.controller,
    required this.textFormFieldKey,
  });

  @override
  State<_Editor> createState() => _EditorState();
}

class _EditorState extends State<_Editor> with WidgetsBindingObserver {
  double _x = 0;
  double _y = 0;
  double _width = 0;
  double _height = 0;
  double _fontSize = 28;
  double _lastScale = 1;

  @override
  void didUpdateWidget(covariant _Editor oldWidget) {
    if (oldWidget.shouldCentered != widget.shouldCentered && widget.shouldCentered) {
      _y = widget.maxHeight * 0.1;
      _x = (widget.maxWidth - _width) / 2;
    }

    if (oldWidget.state.textField.letterSpacing != widget.state.textField.letterSpacing) {
      _getActualTextWidthAndSetNewWidthAndXParams();
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(_setFocusedState);
      widget.focusNode.requestFocus();
    });

    widget.focusNode.addListener(() {
      setState(() {
        if (widget.focusNode.hasFocus) {
          _setFocusedState();
        } else {
          _getActualTextWidthAndSetNewWidthAndXParams();
        }
      });
    });

    final cubit = context.read<FontsEditorCubit?>();

    widget.controller.addListener(() {
      cubit?.onTextUpdated(widget.controller.text);
    });
  }

  void _setFocusedState() {
    _y = widget.maxHeight * 0.1;
    _width = widget.maxWidth;
    _x = 0;
  }

  @override
  Widget build(BuildContext context) {
    final textFiledState = widget.state.textField;

    return Stack(
      children: [
        GestureDetector(
          onTap: widget.focusNode.unfocus,
          onScaleStart: _onScaleStart,
          onScaleUpdate: (details) => _onScaleUpdate(details, false),
          behavior: HitTestBehavior.opaque,
          child: Stack(
            children: [
              Positioned(
                left: _x,
                top: _y,
                child: GestureDetector(
                  onTap: widget.focusNode.unfocus,
                  onScaleStart: _onScaleStart,
                  onScaleUpdate: (details) => _onScaleUpdate(details, widget.state.dragAndDropEnabled),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: _width, maxHeight: widget.maxHeight),
                    child: RepaintBoundary(
                      key: widget.textFormFieldKey,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: ImageFiltered(
                          imageFilter: dart.ImageFilter.blur(
                            sigmaX: textFiledState.blur,
                            sigmaY: textFiledState.blur,
                          ),
                          child: TextFormField(
                            textCapitalization: _getTextCapitalization(textFiledState.fontCaseType),
                            scrollPhysics: const NeverScrollableScrollPhysics(),
                            controller: widget.controller,
                            focusNode: widget.focusNode,
                            cursorColor: textFiledState.cursorColor,
                            maxLines: null,
                            textAlign: textFiledState.textAlign,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              errorBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              hintText: LocaleKeys.writeTextHere.tr(),
                              hintStyle: context.texts.h4Light.copyWith(
                                color: context.colors.gray5,
                                fontSize: _fontSize,
                                fontWeight: FontWeight.w400,
                                shadows: [],
                              ),
                            ),
                            style: TextStyle(
                              package: uiKitPackage,
                              decorationThickness: 0,
                              fontFamily: textFiledState.currentFontModel.fontFamily,
                              color: textFiledState.textColor,
                              fontSize: _fontSize,
                              fontWeight: textFiledState.currentFontModel.selectedFontSubtype.weight,
                              fontStyle: textFiledState.currentFontModel.selectedFontSubtype ==
                                      CustomFontsSubcategoryType.italic
                                  ? FontStyle.italic
                                  : FontStyle.normal,
                              letterSpacing: textFiledState.letterSpacing,
                              height: textFiledState.lineHeight,
                              shadows: [
                                if (textFiledState.shadow.show)
                                  Shadow(
                                    color:
                                        textFiledState.shadow.color.withAlpha(textFiledState.shadow.intensity.toInt()),
                                    blurRadius: textFiledState.shadow.blur,
                                    offset: Offset(textFiledState.shadow.x, textFiledState.shadow.y),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  TextCapitalization _getTextCapitalization(FontCaseType fontCaseType) => switch (fontCaseType) {
        FontCaseType.uppercase => TextCapitalization.characters,
        FontCaseType.lowercase => TextCapitalization.none,
        FontCaseType.standard => TextCapitalization.sentences,
      };

  void _onScaleStart(ScaleStartDetails details) {
    _lastScale = 1.0;
  }

  void _onScaleUpdate(ScaleUpdateDetails details, bool dragAndDropEnabled) {
    setState(() {
      _handleScaling(details);
      if (dragAndDropEnabled) _handlingDragging(details);
    });
  }

  // Handle scaling
  void _handleScaling(ScaleUpdateDetails details) {
    if ((details.scale - 1.0).abs() > 0.01) {
      final scaleDelta = (details.scale - _lastScale).clamp(-0.05, 0.05);
      _fontSize = (_fontSize + scaleDelta * 50).clamp(8.0, 70);

      // Need to check width of resized text and set it to [_width] variable, also every resize might be with
      // updating _x to center text field
      _getActualTextWidthAndSetNewWidthAndXParams();

      _lastScale = details.scale;
    }
  }

  void _handlingDragging(ScaleUpdateDetails details) {
    // Handle dragging
    _x += details.focalPointDelta.dx;
    _y += details.focalPointDelta.dy;

    // Clamp to keep the text inside screen bounds
    _x = _x.clamp(-18, widget.maxWidth - _width + 18);
    _y = _y.clamp(-18, widget.maxHeight - _height + 50);
  }

  // Getting actual text width
  (double, double) _getTextPainterSize() {
    final textFiledState = widget.state.textField;

    if (widget.controller.text.isNotEmpty) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: widget.controller.text,
          style: TextStyle(
            decorationThickness: 0,
            package: uiKitPackage,
            fontFamily: textFiledState.currentFontModel.fontFamily,
            fontSize: _fontSize,
            fontWeight: textFiledState.currentFontModel.selectedFontSubtype.weight,
            letterSpacing: textFiledState.letterSpacing,
            height: textFiledState.lineHeight,
          ),
        ),
        textDirection: dart.TextDirection.ltr,
      )..layout();

      return (textPainter.width, textPainter.height);
    } else {
      final textPainter = TextPainter(
        text: TextSpan(
          text: widget.controller.text.isNotEmpty ? widget.controller.text : LocaleKeys.writeTextHere.tr(),
          style: context.texts.h4Light.copyWith(
            fontSize: _fontSize,
            fontWeight: FontWeight.w400,
          ),
        ),
        textDirection: dart.TextDirection.ltr,
      )..layout();

      return (textPainter.width, textPainter.height);
    }
  }

  void _getActualTextWidthAndSetNewWidthAndXParams() {
    final (width, height) = _getTextPainterSize();
    _height = height;
    _width = (width + 40).clamp(0, widget.maxWidth);
    _x = (widget.maxWidth - _width) / 2;
  }
}
