part of 'fonts_editor_cubit.dart';

enum SnackBarType {
  fontDownloadSuccess,
  fontDownloadFailed,
  copyToClipboardSuccess,
  copyToClipboardFailed,
}

@freezed
class FontsEditorState with _$FontsEditorState {
  const FontsEditorState._();

  const factory FontsEditorState.empty() = FontsEditorEmptyState;

  const factory FontsEditorState.initial({
    required CustomFontModel initialFont,
    required AppBarState appBarState,
    required bool dragAndDropEnabled,
    required TextFieldState textField,
    required FontsSupportType fontsSupportType,
    required bool showFavoriteInFontsSelectTab,
    int? bottomBarSelectedIndex,
  }) = FontsEditorInitialState;

  const factory FontsEditorState.snackbar({required SnackBarType type}) = FontsEditorSnackbarState;

  String get currentFontFamily => maybeMap(
        initial: (state) => state.textField.currentFontModel.fontFamily,
        orElse: () => '',
      );

  String get currentFontTitleMessage => maybeMap(
        initial: (state) => state.textField.currentFontModel.fontTitleMessage,
        orElse: () => '',
      );
}

@freezed
class TextFieldState with _$TextFieldState {
  const factory TextFieldState({
    required CustomFontModel currentFontModel,
    required ShadowState shadow,
    @Default('') String text,
    @Default(false) bool deletedText,
    @Default(Colors.white) Color textColor,
    @Default(Colors.white) Color cursorColor,
    @Default(TextAlign.center) TextAlign textAlign,
    @Default(FontCaseType.standard) FontCaseType fontCaseType,
    @Default(1) double lineHeight,
    @Default(0) double letterSpacing,
    @Default(0) double blur,
  }) = _TextFieldState;
}

@freezed
class AppBarState with _$AppBarState {
  const factory AppBarState({
    required AppBarStateType type,
    required bool forwardButtonActive,
    required bool backButtonActive,
  }) = _AppBarState;
}

@freezed
class ShadowState with _$ShadowState {
  const factory ShadowState({
    required bool show,
    required Color color,
    required double blur,
    required double intensity,
    required double x,
    required double y,
  }) = _ShadowState;

  factory ShadowState.initial() => const _ShadowState(
        show: false,
        color: Colors.white,
        blur: 1,
        intensity: 1,
        x: 0,
        y: 0,
      );

  factory ShadowState.start() => const _ShadowState(
        show: true,
        color: Colors.white,
        blur: 5,
        intensity: 255,
        x: 5,
        y: 5,
      );
}

enum AppBarStateType {
  writing,
  idle,
}
