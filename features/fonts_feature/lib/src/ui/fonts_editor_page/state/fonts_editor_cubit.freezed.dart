// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fonts_editor_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FontsEditorState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)
        initial,
    required TResult Function(SnackBarType type) snackbar,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult? Function(SnackBarType type)? snackbar,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult Function(SnackBarType type)? snackbar,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FontsEditorEmptyState value) empty,
    required TResult Function(FontsEditorInitialState value) initial,
    required TResult Function(FontsEditorSnackbarState value) snackbar,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FontsEditorEmptyState value)? empty,
    TResult? Function(FontsEditorInitialState value)? initial,
    TResult? Function(FontsEditorSnackbarState value)? snackbar,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FontsEditorEmptyState value)? empty,
    TResult Function(FontsEditorInitialState value)? initial,
    TResult Function(FontsEditorSnackbarState value)? snackbar,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FontsEditorStateCopyWith<$Res> {
  factory $FontsEditorStateCopyWith(FontsEditorState value, $Res Function(FontsEditorState) then) =
      _$FontsEditorStateCopyWithImpl<$Res, FontsEditorState>;
}

/// @nodoc
class _$FontsEditorStateCopyWithImpl<$Res, $Val extends FontsEditorState> implements $FontsEditorStateCopyWith<$Res> {
  _$FontsEditorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$FontsEditorEmptyStateImplCopyWith<$Res> {
  factory _$$FontsEditorEmptyStateImplCopyWith(
          _$FontsEditorEmptyStateImpl value, $Res Function(_$FontsEditorEmptyStateImpl) then) =
      __$$FontsEditorEmptyStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FontsEditorEmptyStateImplCopyWithImpl<$Res>
    extends _$FontsEditorStateCopyWithImpl<$Res, _$FontsEditorEmptyStateImpl>
    implements _$$FontsEditorEmptyStateImplCopyWith<$Res> {
  __$$FontsEditorEmptyStateImplCopyWithImpl(
      _$FontsEditorEmptyStateImpl _value, $Res Function(_$FontsEditorEmptyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FontsEditorEmptyStateImpl extends FontsEditorEmptyState with DiagnosticableTreeMixin {
  const _$FontsEditorEmptyStateImpl() : super._();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FontsEditorState.empty()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'FontsEditorState.empty'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) || (other.runtimeType == runtimeType && other is _$FontsEditorEmptyStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)
        initial,
    required TResult Function(SnackBarType type) snackbar,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult? Function(SnackBarType type)? snackbar,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult Function(SnackBarType type)? snackbar,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FontsEditorEmptyState value) empty,
    required TResult Function(FontsEditorInitialState value) initial,
    required TResult Function(FontsEditorSnackbarState value) snackbar,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FontsEditorEmptyState value)? empty,
    TResult? Function(FontsEditorInitialState value)? initial,
    TResult? Function(FontsEditorSnackbarState value)? snackbar,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FontsEditorEmptyState value)? empty,
    TResult Function(FontsEditorInitialState value)? initial,
    TResult Function(FontsEditorSnackbarState value)? snackbar,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class FontsEditorEmptyState extends FontsEditorState {
  const factory FontsEditorEmptyState() = _$FontsEditorEmptyStateImpl;
  const FontsEditorEmptyState._() : super._();
}

/// @nodoc
abstract class _$$FontsEditorInitialStateImplCopyWith<$Res> {
  factory _$$FontsEditorInitialStateImplCopyWith(
          _$FontsEditorInitialStateImpl value, $Res Function(_$FontsEditorInitialStateImpl) then) =
      __$$FontsEditorInitialStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {CustomFontModel initialFont,
      AppBarState appBarState,
      bool dragAndDropEnabled,
      TextFieldState textField,
      FontsSupportType fontsSupportType,
      bool showFavoriteInFontsSelectTab,
      int? bottomBarSelectedIndex});

  $CustomFontModelCopyWith<$Res> get initialFont;
  $AppBarStateCopyWith<$Res> get appBarState;
  $TextFieldStateCopyWith<$Res> get textField;
}

/// @nodoc
class __$$FontsEditorInitialStateImplCopyWithImpl<$Res>
    extends _$FontsEditorStateCopyWithImpl<$Res, _$FontsEditorInitialStateImpl>
    implements _$$FontsEditorInitialStateImplCopyWith<$Res> {
  __$$FontsEditorInitialStateImplCopyWithImpl(
      _$FontsEditorInitialStateImpl _value, $Res Function(_$FontsEditorInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialFont = null,
    Object? appBarState = null,
    Object? dragAndDropEnabled = null,
    Object? textField = null,
    Object? fontsSupportType = null,
    Object? showFavoriteInFontsSelectTab = null,
    Object? bottomBarSelectedIndex = freezed,
  }) {
    return _then(_$FontsEditorInitialStateImpl(
      initialFont: null == initialFont
          ? _value.initialFont
          : initialFont // ignore: cast_nullable_to_non_nullable
              as CustomFontModel,
      appBarState: null == appBarState
          ? _value.appBarState
          : appBarState // ignore: cast_nullable_to_non_nullable
              as AppBarState,
      dragAndDropEnabled: null == dragAndDropEnabled
          ? _value.dragAndDropEnabled
          : dragAndDropEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      textField: null == textField
          ? _value.textField
          : textField // ignore: cast_nullable_to_non_nullable
              as TextFieldState,
      fontsSupportType: null == fontsSupportType
          ? _value.fontsSupportType
          : fontsSupportType // ignore: cast_nullable_to_non_nullable
              as FontsSupportType,
      showFavoriteInFontsSelectTab: null == showFavoriteInFontsSelectTab
          ? _value.showFavoriteInFontsSelectTab
          : showFavoriteInFontsSelectTab // ignore: cast_nullable_to_non_nullable
              as bool,
      bottomBarSelectedIndex: freezed == bottomBarSelectedIndex
          ? _value.bottomBarSelectedIndex
          : bottomBarSelectedIndex // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomFontModelCopyWith<$Res> get initialFont {
    return $CustomFontModelCopyWith<$Res>(_value.initialFont, (value) {
      return _then(_value.copyWith(initialFont: value));
    });
  }

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppBarStateCopyWith<$Res> get appBarState {
    return $AppBarStateCopyWith<$Res>(_value.appBarState, (value) {
      return _then(_value.copyWith(appBarState: value));
    });
  }

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TextFieldStateCopyWith<$Res> get textField {
    return $TextFieldStateCopyWith<$Res>(_value.textField, (value) {
      return _then(_value.copyWith(textField: value));
    });
  }
}

/// @nodoc

class _$FontsEditorInitialStateImpl extends FontsEditorInitialState with DiagnosticableTreeMixin {
  const _$FontsEditorInitialStateImpl(
      {required this.initialFont,
      required this.appBarState,
      required this.dragAndDropEnabled,
      required this.textField,
      required this.fontsSupportType,
      required this.showFavoriteInFontsSelectTab,
      this.bottomBarSelectedIndex})
      : super._();

  @override
  final CustomFontModel initialFont;
  @override
  final AppBarState appBarState;
  @override
  final bool dragAndDropEnabled;
  @override
  final TextFieldState textField;
  @override
  final FontsSupportType fontsSupportType;
  @override
  final bool showFavoriteInFontsSelectTab;
  @override
  final int? bottomBarSelectedIndex;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FontsEditorState.initial(initialFont: $initialFont, appBarState: $appBarState, dragAndDropEnabled: $dragAndDropEnabled, textField: $textField, fontsSupportType: $fontsSupportType, showFavoriteInFontsSelectTab: $showFavoriteInFontsSelectTab, bottomBarSelectedIndex: $bottomBarSelectedIndex)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'FontsEditorState.initial'))
      ..add(DiagnosticsProperty('initialFont', initialFont))
      ..add(DiagnosticsProperty('appBarState', appBarState))
      ..add(DiagnosticsProperty('dragAndDropEnabled', dragAndDropEnabled))
      ..add(DiagnosticsProperty('textField', textField))
      ..add(DiagnosticsProperty('fontsSupportType', fontsSupportType))
      ..add(DiagnosticsProperty('showFavoriteInFontsSelectTab', showFavoriteInFontsSelectTab))
      ..add(DiagnosticsProperty('bottomBarSelectedIndex', bottomBarSelectedIndex));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FontsEditorInitialStateImpl &&
            (identical(other.initialFont, initialFont) || other.initialFont == initialFont) &&
            (identical(other.appBarState, appBarState) || other.appBarState == appBarState) &&
            (identical(other.dragAndDropEnabled, dragAndDropEnabled) ||
                other.dragAndDropEnabled == dragAndDropEnabled) &&
            (identical(other.textField, textField) || other.textField == textField) &&
            (identical(other.fontsSupportType, fontsSupportType) || other.fontsSupportType == fontsSupportType) &&
            (identical(other.showFavoriteInFontsSelectTab, showFavoriteInFontsSelectTab) ||
                other.showFavoriteInFontsSelectTab == showFavoriteInFontsSelectTab) &&
            (identical(other.bottomBarSelectedIndex, bottomBarSelectedIndex) ||
                other.bottomBarSelectedIndex == bottomBarSelectedIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialFont, appBarState, dragAndDropEnabled, textField,
      fontsSupportType, showFavoriteInFontsSelectTab, bottomBarSelectedIndex);

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FontsEditorInitialStateImplCopyWith<_$FontsEditorInitialStateImpl> get copyWith =>
      __$$FontsEditorInitialStateImplCopyWithImpl<_$FontsEditorInitialStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)
        initial,
    required TResult Function(SnackBarType type) snackbar,
  }) {
    return initial(initialFont, appBarState, dragAndDropEnabled, textField, fontsSupportType,
        showFavoriteInFontsSelectTab, bottomBarSelectedIndex);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult? Function(SnackBarType type)? snackbar,
  }) {
    return initial?.call(initialFont, appBarState, dragAndDropEnabled, textField, fontsSupportType,
        showFavoriteInFontsSelectTab, bottomBarSelectedIndex);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult Function(SnackBarType type)? snackbar,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(initialFont, appBarState, dragAndDropEnabled, textField, fontsSupportType,
          showFavoriteInFontsSelectTab, bottomBarSelectedIndex);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FontsEditorEmptyState value) empty,
    required TResult Function(FontsEditorInitialState value) initial,
    required TResult Function(FontsEditorSnackbarState value) snackbar,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FontsEditorEmptyState value)? empty,
    TResult? Function(FontsEditorInitialState value)? initial,
    TResult? Function(FontsEditorSnackbarState value)? snackbar,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FontsEditorEmptyState value)? empty,
    TResult Function(FontsEditorInitialState value)? initial,
    TResult Function(FontsEditorSnackbarState value)? snackbar,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class FontsEditorInitialState extends FontsEditorState {
  const factory FontsEditorInitialState(
      {required final CustomFontModel initialFont,
      required final AppBarState appBarState,
      required final bool dragAndDropEnabled,
      required final TextFieldState textField,
      required final FontsSupportType fontsSupportType,
      required final bool showFavoriteInFontsSelectTab,
      final int? bottomBarSelectedIndex}) = _$FontsEditorInitialStateImpl;
  const FontsEditorInitialState._() : super._();

  CustomFontModel get initialFont;
  AppBarState get appBarState;
  bool get dragAndDropEnabled;
  TextFieldState get textField;
  FontsSupportType get fontsSupportType;
  bool get showFavoriteInFontsSelectTab;
  int? get bottomBarSelectedIndex;

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FontsEditorInitialStateImplCopyWith<_$FontsEditorInitialStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FontsEditorSnackbarStateImplCopyWith<$Res> {
  factory _$$FontsEditorSnackbarStateImplCopyWith(
          _$FontsEditorSnackbarStateImpl value, $Res Function(_$FontsEditorSnackbarStateImpl) then) =
      __$$FontsEditorSnackbarStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SnackBarType type});
}

/// @nodoc
class __$$FontsEditorSnackbarStateImplCopyWithImpl<$Res>
    extends _$FontsEditorStateCopyWithImpl<$Res, _$FontsEditorSnackbarStateImpl>
    implements _$$FontsEditorSnackbarStateImplCopyWith<$Res> {
  __$$FontsEditorSnackbarStateImplCopyWithImpl(
      _$FontsEditorSnackbarStateImpl _value, $Res Function(_$FontsEditorSnackbarStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$FontsEditorSnackbarStateImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as SnackBarType,
    ));
  }
}

/// @nodoc

class _$FontsEditorSnackbarStateImpl extends FontsEditorSnackbarState with DiagnosticableTreeMixin {
  const _$FontsEditorSnackbarStateImpl({required this.type}) : super._();

  @override
  final SnackBarType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FontsEditorState.snackbar(type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'FontsEditorState.snackbar'))
      ..add(DiagnosticsProperty('type', type));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FontsEditorSnackbarStateImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FontsEditorSnackbarStateImplCopyWith<_$FontsEditorSnackbarStateImpl> get copyWith =>
      __$$FontsEditorSnackbarStateImplCopyWithImpl<_$FontsEditorSnackbarStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)
        initial,
    required TResult Function(SnackBarType type) snackbar,
  }) {
    return snackbar(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult? Function(SnackBarType type)? snackbar,
  }) {
    return snackbar?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            CustomFontModel initialFont,
            AppBarState appBarState,
            bool dragAndDropEnabled,
            TextFieldState textField,
            FontsSupportType fontsSupportType,
            bool showFavoriteInFontsSelectTab,
            int? bottomBarSelectedIndex)?
        initial,
    TResult Function(SnackBarType type)? snackbar,
    required TResult orElse(),
  }) {
    if (snackbar != null) {
      return snackbar(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FontsEditorEmptyState value) empty,
    required TResult Function(FontsEditorInitialState value) initial,
    required TResult Function(FontsEditorSnackbarState value) snackbar,
  }) {
    return snackbar(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FontsEditorEmptyState value)? empty,
    TResult? Function(FontsEditorInitialState value)? initial,
    TResult? Function(FontsEditorSnackbarState value)? snackbar,
  }) {
    return snackbar?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FontsEditorEmptyState value)? empty,
    TResult Function(FontsEditorInitialState value)? initial,
    TResult Function(FontsEditorSnackbarState value)? snackbar,
    required TResult orElse(),
  }) {
    if (snackbar != null) {
      return snackbar(this);
    }
    return orElse();
  }
}

abstract class FontsEditorSnackbarState extends FontsEditorState {
  const factory FontsEditorSnackbarState({required final SnackBarType type}) = _$FontsEditorSnackbarStateImpl;
  const FontsEditorSnackbarState._() : super._();

  SnackBarType get type;

  /// Create a copy of FontsEditorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FontsEditorSnackbarStateImplCopyWith<_$FontsEditorSnackbarStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TextFieldState {
  CustomFontModel get currentFontModel => throw _privateConstructorUsedError;
  ShadowState get shadow => throw _privateConstructorUsedError;
  String get text => throw _privateConstructorUsedError;
  bool get deletedText => throw _privateConstructorUsedError;
  Color get textColor => throw _privateConstructorUsedError;
  Color get cursorColor => throw _privateConstructorUsedError;
  TextAlign get textAlign => throw _privateConstructorUsedError;
  FontCaseType get fontCaseType => throw _privateConstructorUsedError;
  double get lineHeight => throw _privateConstructorUsedError;
  double get letterSpacing => throw _privateConstructorUsedError;
  double get blur => throw _privateConstructorUsedError;

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TextFieldStateCopyWith<TextFieldState> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TextFieldStateCopyWith<$Res> {
  factory $TextFieldStateCopyWith(TextFieldState value, $Res Function(TextFieldState) then) =
      _$TextFieldStateCopyWithImpl<$Res, TextFieldState>;
  @useResult
  $Res call(
      {CustomFontModel currentFontModel,
      ShadowState shadow,
      String text,
      bool deletedText,
      Color textColor,
      Color cursorColor,
      TextAlign textAlign,
      FontCaseType fontCaseType,
      double lineHeight,
      double letterSpacing,
      double blur});

  $CustomFontModelCopyWith<$Res> get currentFontModel;
  $ShadowStateCopyWith<$Res> get shadow;
}

/// @nodoc
class _$TextFieldStateCopyWithImpl<$Res, $Val extends TextFieldState> implements $TextFieldStateCopyWith<$Res> {
  _$TextFieldStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentFontModel = null,
    Object? shadow = null,
    Object? text = null,
    Object? deletedText = null,
    Object? textColor = null,
    Object? cursorColor = null,
    Object? textAlign = null,
    Object? fontCaseType = null,
    Object? lineHeight = null,
    Object? letterSpacing = null,
    Object? blur = null,
  }) {
    return _then(_value.copyWith(
      currentFontModel: null == currentFontModel
          ? _value.currentFontModel
          : currentFontModel // ignore: cast_nullable_to_non_nullable
              as CustomFontModel,
      shadow: null == shadow
          ? _value.shadow
          : shadow // ignore: cast_nullable_to_non_nullable
              as ShadowState,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      deletedText: null == deletedText
          ? _value.deletedText
          : deletedText // ignore: cast_nullable_to_non_nullable
              as bool,
      textColor: null == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color,
      cursorColor: null == cursorColor
          ? _value.cursorColor
          : cursorColor // ignore: cast_nullable_to_non_nullable
              as Color,
      textAlign: null == textAlign
          ? _value.textAlign
          : textAlign // ignore: cast_nullable_to_non_nullable
              as TextAlign,
      fontCaseType: null == fontCaseType
          ? _value.fontCaseType
          : fontCaseType // ignore: cast_nullable_to_non_nullable
              as FontCaseType,
      lineHeight: null == lineHeight
          ? _value.lineHeight
          : lineHeight // ignore: cast_nullable_to_non_nullable
              as double,
      letterSpacing: null == letterSpacing
          ? _value.letterSpacing
          : letterSpacing // ignore: cast_nullable_to_non_nullable
              as double,
      blur: null == blur
          ? _value.blur
          : blur // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomFontModelCopyWith<$Res> get currentFontModel {
    return $CustomFontModelCopyWith<$Res>(_value.currentFontModel, (value) {
      return _then(_value.copyWith(currentFontModel: value) as $Val);
    });
  }

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShadowStateCopyWith<$Res> get shadow {
    return $ShadowStateCopyWith<$Res>(_value.shadow, (value) {
      return _then(_value.copyWith(shadow: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TextFieldStateImplCopyWith<$Res> implements $TextFieldStateCopyWith<$Res> {
  factory _$$TextFieldStateImplCopyWith(_$TextFieldStateImpl value, $Res Function(_$TextFieldStateImpl) then) =
      __$$TextFieldStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CustomFontModel currentFontModel,
      ShadowState shadow,
      String text,
      bool deletedText,
      Color textColor,
      Color cursorColor,
      TextAlign textAlign,
      FontCaseType fontCaseType,
      double lineHeight,
      double letterSpacing,
      double blur});

  @override
  $CustomFontModelCopyWith<$Res> get currentFontModel;
  @override
  $ShadowStateCopyWith<$Res> get shadow;
}

/// @nodoc
class __$$TextFieldStateImplCopyWithImpl<$Res> extends _$TextFieldStateCopyWithImpl<$Res, _$TextFieldStateImpl>
    implements _$$TextFieldStateImplCopyWith<$Res> {
  __$$TextFieldStateImplCopyWithImpl(_$TextFieldStateImpl _value, $Res Function(_$TextFieldStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentFontModel = null,
    Object? shadow = null,
    Object? text = null,
    Object? deletedText = null,
    Object? textColor = null,
    Object? cursorColor = null,
    Object? textAlign = null,
    Object? fontCaseType = null,
    Object? lineHeight = null,
    Object? letterSpacing = null,
    Object? blur = null,
  }) {
    return _then(_$TextFieldStateImpl(
      currentFontModel: null == currentFontModel
          ? _value.currentFontModel
          : currentFontModel // ignore: cast_nullable_to_non_nullable
              as CustomFontModel,
      shadow: null == shadow
          ? _value.shadow
          : shadow // ignore: cast_nullable_to_non_nullable
              as ShadowState,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      deletedText: null == deletedText
          ? _value.deletedText
          : deletedText // ignore: cast_nullable_to_non_nullable
              as bool,
      textColor: null == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color,
      cursorColor: null == cursorColor
          ? _value.cursorColor
          : cursorColor // ignore: cast_nullable_to_non_nullable
              as Color,
      textAlign: null == textAlign
          ? _value.textAlign
          : textAlign // ignore: cast_nullable_to_non_nullable
              as TextAlign,
      fontCaseType: null == fontCaseType
          ? _value.fontCaseType
          : fontCaseType // ignore: cast_nullable_to_non_nullable
              as FontCaseType,
      lineHeight: null == lineHeight
          ? _value.lineHeight
          : lineHeight // ignore: cast_nullable_to_non_nullable
              as double,
      letterSpacing: null == letterSpacing
          ? _value.letterSpacing
          : letterSpacing // ignore: cast_nullable_to_non_nullable
              as double,
      blur: null == blur
          ? _value.blur
          : blur // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$TextFieldStateImpl with DiagnosticableTreeMixin implements _TextFieldState {
  const _$TextFieldStateImpl(
      {required this.currentFontModel,
      required this.shadow,
      this.text = '',
      this.deletedText = false,
      this.textColor = Colors.white,
      this.cursorColor = Colors.white,
      this.textAlign = TextAlign.center,
      this.fontCaseType = FontCaseType.standard,
      this.lineHeight = 1,
      this.letterSpacing = 0,
      this.blur = 0});

  @override
  final CustomFontModel currentFontModel;
  @override
  final ShadowState shadow;
  @override
  @JsonKey()
  final String text;
  @override
  @JsonKey()
  final bool deletedText;
  @override
  @JsonKey()
  final Color textColor;
  @override
  @JsonKey()
  final Color cursorColor;
  @override
  @JsonKey()
  final TextAlign textAlign;
  @override
  @JsonKey()
  final FontCaseType fontCaseType;
  @override
  @JsonKey()
  final double lineHeight;
  @override
  @JsonKey()
  final double letterSpacing;
  @override
  @JsonKey()
  final double blur;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TextFieldState(currentFontModel: $currentFontModel, shadow: $shadow, text: $text, deletedText: $deletedText, textColor: $textColor, cursorColor: $cursorColor, textAlign: $textAlign, fontCaseType: $fontCaseType, lineHeight: $lineHeight, letterSpacing: $letterSpacing, blur: $blur)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TextFieldState'))
      ..add(DiagnosticsProperty('currentFontModel', currentFontModel))
      ..add(DiagnosticsProperty('shadow', shadow))
      ..add(DiagnosticsProperty('text', text))
      ..add(DiagnosticsProperty('deletedText', deletedText))
      ..add(DiagnosticsProperty('textColor', textColor))
      ..add(DiagnosticsProperty('cursorColor', cursorColor))
      ..add(DiagnosticsProperty('textAlign', textAlign))
      ..add(DiagnosticsProperty('fontCaseType', fontCaseType))
      ..add(DiagnosticsProperty('lineHeight', lineHeight))
      ..add(DiagnosticsProperty('letterSpacing', letterSpacing))
      ..add(DiagnosticsProperty('blur', blur));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextFieldStateImpl &&
            (identical(other.currentFontModel, currentFontModel) || other.currentFontModel == currentFontModel) &&
            (identical(other.shadow, shadow) || other.shadow == shadow) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.deletedText, deletedText) || other.deletedText == deletedText) &&
            (identical(other.textColor, textColor) || other.textColor == textColor) &&
            (identical(other.cursorColor, cursorColor) || other.cursorColor == cursorColor) &&
            (identical(other.textAlign, textAlign) || other.textAlign == textAlign) &&
            (identical(other.fontCaseType, fontCaseType) || other.fontCaseType == fontCaseType) &&
            (identical(other.lineHeight, lineHeight) || other.lineHeight == lineHeight) &&
            (identical(other.letterSpacing, letterSpacing) || other.letterSpacing == letterSpacing) &&
            (identical(other.blur, blur) || other.blur == blur));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentFontModel, shadow, text, deletedText, textColor, cursorColor,
      textAlign, fontCaseType, lineHeight, letterSpacing, blur);

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TextFieldStateImplCopyWith<_$TextFieldStateImpl> get copyWith =>
      __$$TextFieldStateImplCopyWithImpl<_$TextFieldStateImpl>(this, _$identity);
}

abstract class _TextFieldState implements TextFieldState {
  const factory _TextFieldState(
      {required final CustomFontModel currentFontModel,
      required final ShadowState shadow,
      final String text,
      final bool deletedText,
      final Color textColor,
      final Color cursorColor,
      final TextAlign textAlign,
      final FontCaseType fontCaseType,
      final double lineHeight,
      final double letterSpacing,
      final double blur}) = _$TextFieldStateImpl;

  @override
  CustomFontModel get currentFontModel;
  @override
  ShadowState get shadow;
  @override
  String get text;
  @override
  bool get deletedText;
  @override
  Color get textColor;
  @override
  Color get cursorColor;
  @override
  TextAlign get textAlign;
  @override
  FontCaseType get fontCaseType;
  @override
  double get lineHeight;
  @override
  double get letterSpacing;
  @override
  double get blur;

  /// Create a copy of TextFieldState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TextFieldStateImplCopyWith<_$TextFieldStateImpl> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AppBarState {
  AppBarStateType get type => throw _privateConstructorUsedError;
  bool get forwardButtonActive => throw _privateConstructorUsedError;
  bool get backButtonActive => throw _privateConstructorUsedError;

  /// Create a copy of AppBarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppBarStateCopyWith<AppBarState> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppBarStateCopyWith<$Res> {
  factory $AppBarStateCopyWith(AppBarState value, $Res Function(AppBarState) then) =
      _$AppBarStateCopyWithImpl<$Res, AppBarState>;
  @useResult
  $Res call({AppBarStateType type, bool forwardButtonActive, bool backButtonActive});
}

/// @nodoc
class _$AppBarStateCopyWithImpl<$Res, $Val extends AppBarState> implements $AppBarStateCopyWith<$Res> {
  _$AppBarStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppBarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? forwardButtonActive = null,
    Object? backButtonActive = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AppBarStateType,
      forwardButtonActive: null == forwardButtonActive
          ? _value.forwardButtonActive
          : forwardButtonActive // ignore: cast_nullable_to_non_nullable
              as bool,
      backButtonActive: null == backButtonActive
          ? _value.backButtonActive
          : backButtonActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppBarStateImplCopyWith<$Res> implements $AppBarStateCopyWith<$Res> {
  factory _$$AppBarStateImplCopyWith(_$AppBarStateImpl value, $Res Function(_$AppBarStateImpl) then) =
      __$$AppBarStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppBarStateType type, bool forwardButtonActive, bool backButtonActive});
}

/// @nodoc
class __$$AppBarStateImplCopyWithImpl<$Res> extends _$AppBarStateCopyWithImpl<$Res, _$AppBarStateImpl>
    implements _$$AppBarStateImplCopyWith<$Res> {
  __$$AppBarStateImplCopyWithImpl(_$AppBarStateImpl _value, $Res Function(_$AppBarStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppBarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? forwardButtonActive = null,
    Object? backButtonActive = null,
  }) {
    return _then(_$AppBarStateImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AppBarStateType,
      forwardButtonActive: null == forwardButtonActive
          ? _value.forwardButtonActive
          : forwardButtonActive // ignore: cast_nullable_to_non_nullable
              as bool,
      backButtonActive: null == backButtonActive
          ? _value.backButtonActive
          : backButtonActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AppBarStateImpl with DiagnosticableTreeMixin implements _AppBarState {
  const _$AppBarStateImpl({required this.type, required this.forwardButtonActive, required this.backButtonActive});

  @override
  final AppBarStateType type;
  @override
  final bool forwardButtonActive;
  @override
  final bool backButtonActive;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppBarState(type: $type, forwardButtonActive: $forwardButtonActive, backButtonActive: $backButtonActive)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppBarState'))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('forwardButtonActive', forwardButtonActive))
      ..add(DiagnosticsProperty('backButtonActive', backButtonActive));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppBarStateImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.forwardButtonActive, forwardButtonActive) ||
                other.forwardButtonActive == forwardButtonActive) &&
            (identical(other.backButtonActive, backButtonActive) || other.backButtonActive == backButtonActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type, forwardButtonActive, backButtonActive);

  /// Create a copy of AppBarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppBarStateImplCopyWith<_$AppBarStateImpl> get copyWith =>
      __$$AppBarStateImplCopyWithImpl<_$AppBarStateImpl>(this, _$identity);
}

abstract class _AppBarState implements AppBarState {
  const factory _AppBarState(
      {required final AppBarStateType type,
      required final bool forwardButtonActive,
      required final bool backButtonActive}) = _$AppBarStateImpl;

  @override
  AppBarStateType get type;
  @override
  bool get forwardButtonActive;
  @override
  bool get backButtonActive;

  /// Create a copy of AppBarState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppBarStateImplCopyWith<_$AppBarStateImpl> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ShadowState {
  bool get show => throw _privateConstructorUsedError;
  Color get color => throw _privateConstructorUsedError;
  double get blur => throw _privateConstructorUsedError;
  double get intensity => throw _privateConstructorUsedError;
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;

  /// Create a copy of ShadowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShadowStateCopyWith<ShadowState> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShadowStateCopyWith<$Res> {
  factory $ShadowStateCopyWith(ShadowState value, $Res Function(ShadowState) then) =
      _$ShadowStateCopyWithImpl<$Res, ShadowState>;
  @useResult
  $Res call({bool show, Color color, double blur, double intensity, double x, double y});
}

/// @nodoc
class _$ShadowStateCopyWithImpl<$Res, $Val extends ShadowState> implements $ShadowStateCopyWith<$Res> {
  _$ShadowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShadowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? show = null,
    Object? color = null,
    Object? blur = null,
    Object? intensity = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_value.copyWith(
      show: null == show
          ? _value.show
          : show // ignore: cast_nullable_to_non_nullable
              as bool,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color,
      blur: null == blur
          ? _value.blur
          : blur // ignore: cast_nullable_to_non_nullable
              as double,
      intensity: null == intensity
          ? _value.intensity
          : intensity // ignore: cast_nullable_to_non_nullable
              as double,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShadowStateImplCopyWith<$Res> implements $ShadowStateCopyWith<$Res> {
  factory _$$ShadowStateImplCopyWith(_$ShadowStateImpl value, $Res Function(_$ShadowStateImpl) then) =
      __$$ShadowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool show, Color color, double blur, double intensity, double x, double y});
}

/// @nodoc
class __$$ShadowStateImplCopyWithImpl<$Res> extends _$ShadowStateCopyWithImpl<$Res, _$ShadowStateImpl>
    implements _$$ShadowStateImplCopyWith<$Res> {
  __$$ShadowStateImplCopyWithImpl(_$ShadowStateImpl _value, $Res Function(_$ShadowStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShadowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? show = null,
    Object? color = null,
    Object? blur = null,
    Object? intensity = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_$ShadowStateImpl(
      show: null == show
          ? _value.show
          : show // ignore: cast_nullable_to_non_nullable
              as bool,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color,
      blur: null == blur
          ? _value.blur
          : blur // ignore: cast_nullable_to_non_nullable
              as double,
      intensity: null == intensity
          ? _value.intensity
          : intensity // ignore: cast_nullable_to_non_nullable
              as double,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$ShadowStateImpl with DiagnosticableTreeMixin implements _ShadowState {
  const _$ShadowStateImpl(
      {required this.show,
      required this.color,
      required this.blur,
      required this.intensity,
      required this.x,
      required this.y});

  @override
  final bool show;
  @override
  final Color color;
  @override
  final double blur;
  @override
  final double intensity;
  @override
  final double x;
  @override
  final double y;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShadowState(show: $show, color: $color, blur: $blur, intensity: $intensity, x: $x, y: $y)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ShadowState'))
      ..add(DiagnosticsProperty('show', show))
      ..add(DiagnosticsProperty('color', color))
      ..add(DiagnosticsProperty('blur', blur))
      ..add(DiagnosticsProperty('intensity', intensity))
      ..add(DiagnosticsProperty('x', x))
      ..add(DiagnosticsProperty('y', y));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShadowStateImpl &&
            (identical(other.show, show) || other.show == show) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.blur, blur) || other.blur == blur) &&
            (identical(other.intensity, intensity) || other.intensity == intensity) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @override
  int get hashCode => Object.hash(runtimeType, show, color, blur, intensity, x, y);

  /// Create a copy of ShadowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShadowStateImplCopyWith<_$ShadowStateImpl> get copyWith =>
      __$$ShadowStateImplCopyWithImpl<_$ShadowStateImpl>(this, _$identity);
}

abstract class _ShadowState implements ShadowState {
  const factory _ShadowState(
      {required final bool show,
      required final Color color,
      required final double blur,
      required final double intensity,
      required final double x,
      required final double y}) = _$ShadowStateImpl;

  @override
  bool get show;
  @override
  Color get color;
  @override
  double get blur;
  @override
  double get intensity;
  @override
  double get x;
  @override
  double get y;

  /// Create a copy of ShadowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShadowStateImplCopyWith<_$ShadowStateImpl> get copyWith => throw _privateConstructorUsedError;
}
