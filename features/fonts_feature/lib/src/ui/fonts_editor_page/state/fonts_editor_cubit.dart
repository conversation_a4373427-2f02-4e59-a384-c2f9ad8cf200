import 'dart:async';
import 'dart:developer';

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fonts_feature/src/domain/interactor/fonts_interactor.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:common/common.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/fonts_editor_analytics.dart';
import 'package:fonts_feature/src/ui/fonts_editor_page/widgets/font_case_selection/font_case_selection_tab.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

part 'fonts_editor_cubit.freezed.dart';
part 'fonts_editor_state.dart';

class FontsEditorCubit extends SafeCubitWrapper<FontsEditorState> with ClipboardMixin, WidgetToBytesMixin {
  final FontsEditorAnalytics _analytics;
  final FontsInteractor _fontsInteractor;
  final AbTestingService _abTestingService;
  final CustomFontsProvider _customFontsProvider;
  late StreamSubscription<bool> _keyboardSubscription;

  final List<TextFieldState> _stateHistoryList = [];
  int _currentIndex = -1;

  FontsEditorCubit({
    required FontsEditorAnalytics analytics,
    required CustomFontsProvider customFontsProvider,
    required AbTestingService abTestingService,
    required FontsInteractor fontsInteractor,
  })  : _analytics = analytics,
        _fontsInteractor = fontsInteractor,
        _abTestingService = abTestingService,
        _customFontsProvider = customFontsProvider,
        super(const FontsEditorState.empty()) {
    _keyboardSubscription = KeyboardVisibilityController().onChange.listen((bool visible) {
      if (visible) {
        _onKeyboardOpened();
      } else {
        _onKeyboardClose();
      }
    });
  }

  void init(CustomFontModel font, bool isFromFavorites) {
    final initState = _getInitialState(font);
    emit(initState.copyWith(showFavoriteInFontsSelectTab: isFromFavorites));
    _saveStateToHistory(initState.textField);
  }

  void historyBackClick() {
    state.mapOrNull(
      initial: (state) {
        if (_currentIndex > 0) {
          _currentIndex--;
          _historyRestore(state);
          _analytics.onHistoryBackClick();
        }
      },
    );
  }

  void historyForwardClick() {
    state.mapOrNull(
      initial: (state) {
        if (_currentIndex < _stateHistoryList.length - 1) {
          _currentIndex++;
          _historyRestore(state);
          _analytics.onHistoryForwardClick();
        }
      },
    );
  }

  void onTextUpdated(String text) {
    state.mapOrNull(
      initial: (state) {
        final textField = state.textField.copyWith(text: text);
        emit(state.copyWith(textField: textField));
      },
    );
  }

  void onBottomBarItemClick(int index) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onBottomBarItemClick(index);
        if (index != state.bottomBarSelectedIndex && index == 5) {
          final textField = state.textField.copyWith(
            shadow: state.textField.shadow.show ? state.textField.shadow : ShadowState.start(),
          );
          emit(
            state.copyWith(
              bottomBarSelectedIndex: index,
              textField: textField,
            ),
          );
          _saveStateToHistory(textField);
        } else {
          emit(
            state.copyWith(
              bottomBarSelectedIndex: index == state.bottomBarSelectedIndex ? null : index,
              showFavoriteInFontsSelectTab: false,
            ),
          );
        }
      },
    );
  }

  void onBottomBarCloseClick() {
    state.mapOrNull(
      initial: (state) {
        _analytics.onBottomBarCloseClick();
        emit(state.copyWith(bottomBarSelectedIndex: null));
      },
    );
  }

  void onLetterSpacingChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.letterSpacing != value) {
          final textField = state.textField.copyWith(letterSpacing: value);
          emit(state.copyWith(textField: textField));
        }
      },
    );
  }

  void onLetterSpacingChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onLetterSpacingChanged(value);
        final textField = state.textField.copyWith(letterSpacing: value);
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onLineSpacingChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.lineHeight != value) {
          final textField = state.textField.copyWith(lineHeight: value);
          emit(state.copyWith(textField: textField));
        }
      },
    );
  }

  void onLineSpacingChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onLineSpacingChanged(value);
        final textField = state.textField.copyWith(lineHeight: value);
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onFontSelected(CustomFontModel font) {
    // Analytics sends from [SelectFontCubit]
    state.mapOrNull(
      initial: (state) {
        final textField = state.textField.copyWith(currentFontModel: font);
        emit(state.copyWith(textField: textField));
      },
    );
  }

  void onLanguageTypeSelect(FontsSupportType type) {
    state.mapOrNull(
      initial: (state) {
        _analytics.fontSupportTypeChosen(type.name);

        if (state.fontsSupportType != type) {
          emit(state.copyWith(fontsSupportType: type));
        }
      },
    );
  }

  void onBackClick() {
    _analytics.onBackClick();
  }

  void onDeleteClick() {
    state.mapOrNull(
      initial: (state) {
        _analytics.onDeleteClick();
        final initialFont = state.initialFont;
        final initialState = _getInitialState(initialFont, deletedText: true);
        emit(initialState);

        // Handle case when previous state is initial and doesn't has text then user click on delete and when return
        // text not restored.
        final previousStateText = _stateHistoryList[_currentIndex];
        if (previousStateText.text.isEmpty && previousStateText.text.isEmpty && state.textField.text.isNotEmpty) {
          _stateHistoryList[_currentIndex] = previousStateText.copyWith(text: state.textField.text);
        }

        if (_currentIndex != 0) {
          _saveStateToHistory(initialState.textField);
        }
      },
    );
  }

  void onFontSubtypeChanged(int index) {
    state.mapOrNull(
      initial: (state) {
        final currentSubtype = state.textField.currentFontModel.selectedFontSubtype;
        final newSubtype = state.textField.currentFontModel.fontTypes[index].type;

        if (currentSubtype != newSubtype) {
          _analytics.onFontSubtypeChanged(newSubtype.name);

          final textField = state.textField
              .copyWith(currentFontModel: state.textField.currentFontModel.copyWith(selectedFontSubtype: newSubtype));
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onSelectColor(Color color) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.textColor != color) {
          _analytics.onSelectColor(color);
          final textField = state.textField.copyWith(textColor: color);
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onSelectAlign(TextAlign textAlign) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.textAlign != textAlign) {
          _analytics.onSelectAlign(textAlign);
          final textField = state.textField.copyWith(textAlign: textAlign);
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onSelectFontCaseType(FontCaseType fontCaseType) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.fontCaseType != fontCaseType) {
          _analytics.onSelectFontCaseType(fontCaseType);
          final updatedText = _applyFontCase(fontCaseType, state.textField.text);
          final textField = state.textField.copyWith(text: updatedText, fontCaseType: fontCaseType);
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onShadowXChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow.x != value) {
          emit(
            state.copyWith(
              textField: state.textField.copyWith(
                shadow: state.textField.shadow.copyWith(x: value, show: true),
              ),
            ),
          );
        }
      },
    );
  }

  void onShadowYChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow.y != value) {
          emit(
            state.copyWith(
              textField: state.textField.copyWith(
                shadow: state.textField.shadow.copyWith(y: value, show: true),
              ),
            ),
          );
        }
      },
    );
  }

  void onShadowIntensityChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow.intensity != value) {
          emit(
            state.copyWith(
              textField: state.textField.copyWith(
                shadow: state.textField.shadow.copyWith(intensity: value, show: true),
              ),
            ),
          );
        }
      },
    );
  }

  void onShadowBlurChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow.blur != value) {
          emit(
            state.copyWith(
              textField: state.textField.copyWith(
                shadow: state.textField.shadow.copyWith(blur: value, show: true),
              ),
            ),
          );
        }
      },
    );
  }

  void onShadowXChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onShadowXChanged(value);
        final textField = state.textField.copyWith(
          shadow: state.textField.shadow.copyWith(x: value, show: true),
        );
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onShadowYChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onShadowYChanged(value);
        final textField = state.textField.copyWith(
          shadow: state.textField.shadow.copyWith(y: value, show: true),
        );
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onShadowIntensityChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onShadowIntensityChanged(value);
        final textField = state.textField.copyWith(
          shadow: state.textField.shadow.copyWith(intensity: value, show: true),
        );
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onShadowBlurChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onShadowBlurChanged(value);
        final textField = state.textField.copyWith(
          shadow: state.textField.shadow.copyWith(blur: value, show: true),
        );
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onShadowColorSelected(Color color) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow.color != color) {
          _analytics.onShadowColorSelected(color);
          final textField = state.textField.copyWith(
            shadow: state.textField.shadow.copyWith(color: color, show: true),
          );
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onBlurChanged(double value) {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.blur != value) {
          final textField = state.textField.copyWith(blur: value);
          emit(state.copyWith(textField: textField));
        }
      },
    );
  }

  void onBlurChangeEnd(double value) {
    state.mapOrNull(
      initial: (state) {
        _analytics.onBlurChanged(value);
        final textField = state.textField.copyWith(blur: value);
        emit(state.copyWith(textField: textField));
        _saveStateToHistory(textField);
      },
    );
  }

  void onBlurResetClick() {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.blur != 0) {
          _analytics.onBlurReset();
          final textField = state.textField.copyWith(blur: 0);
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  void onShadowResetClick() {
    state.mapOrNull(
      initial: (state) {
        if (state.textField.shadow != ShadowState.initial()) {
          _analytics.onShadowResetClick();
          final textField = state.textField.copyWith(
            shadow: ShadowState.initial(),
          );
          emit(state.copyWith(textField: textField));
          _saveStateToHistory(textField);
        }
      },
    );
  }

  Future<void> onCopyToClipboardClick(GlobalKey inputFieldKey) async {
    await state.mapOrNull(
      initial: (state) async {
        final updatedState = state.copyWith(
          appBarState: state.appBarState.copyWith(type: AppBarStateType.idle),
          dragAndDropEnabled: true,
        );
        final currentFontFamily = updatedState.currentFontFamily;
        final categoryName = _findFontCategory(currentFontFamily);

        _analytics.onCopyToClipboardClick(
          currentFontFamily,
          updatedState.currentFontTitleMessage,
          categoryName,
        );

        // Need to hide cursor before making an image
        emit(updatedState.copyWith(textField: updatedState.textField.copyWith(cursorColor: Colors.transparent)));
        await Future.delayed(const Duration(milliseconds: 150));
        final bytes = await createImageFromWidget(key: inputFieldKey);
        emit(updatedState.copyWith(textField: updatedState.textField.copyWith(cursorColor: Colors.white)));
        if (bytes != null) {
          try {
            await copyToClipboardPngByBytes(bytes);

            if (isIOS) {
              emit(const FontsEditorState.snackbar(type: SnackBarType.copyToClipboardSuccess));
              emit(updatedState);
            }
            // Log A/B testing event
            _togglePositiveCaseAbTest();
          } on Object {
            emit(const FontsEditorState.snackbar(type: SnackBarType.copyToClipboardFailed));
            emit(updatedState);
            log('Something goes wrong with copying to clipboard');
          }
        } else {
          emit(const FontsEditorState.snackbar(type: SnackBarType.copyToClipboardFailed));
          emit(updatedState);
          log('Something goes wrong with generating image');
        }
      },
    );
  }

  void onCopyToClipboardClickButUserNotSubscribed() {
    state.mapOrNull(
      initial: (state) {
        final currentFontFamily = state.currentFontFamily;
        final categoryName = _findFontCategory(currentFontFamily);

        _analytics.onCopyToClipboardClickButUserNotSubscribed(
          currentFontFamily,
          state.currentFontTitleMessage,
          categoryName,
        );
      },
    );
  }

  void onDownloadClickButUserNotSubscribed() {
    state.mapOrNull(
      initial: (state) {
        final currentFontFamily = state.currentFontFamily;
        final categoryName = _findFontCategory(currentFontFamily);

        _analytics.onDownloadClickButUserNotSubscribed(
          currentFontFamily,
          state.currentFontTitleMessage,
          categoryName,
        );
      },
    );
  }

  Future<void> onDownloadClick(GlobalKey inputFieldKey) async {
    await state.mapOrNull(
      initial: (state) async {
        final updatedState = state.copyWith(
          appBarState: state.appBarState.copyWith(type: AppBarStateType.idle),
          dragAndDropEnabled: true,
        );
        final currentFontFamily = updatedState.currentFontFamily;
        final categoryName = _findFontCategory(currentFontFamily);

        _analytics.onDownloadClick(
          currentFontFamily,
          updatedState.currentFontTitleMessage,
          categoryName,
        );

        // Need to hide cursor before making an image
        emit(updatedState.copyWith(textField: updatedState.textField.copyWith(cursorColor: Colors.transparent)));
        await Future.delayed(const Duration(milliseconds: 150));
        final bytes = await createImageFromWidget(key: inputFieldKey);
        emit(updatedState.copyWith(textField: updatedState.textField.copyWith(cursorColor: Colors.white)));

        if (bytes != null) {
          final result = await startSafely(
            _fontsInteractor.saveFontToGallery(bytes: bytes),
          );
          result.fold(
            (failure) => _handleDownloadFailed(failure),
            (stickerPath) => _handleDownloadSuccess(),
          );
        } else {
          emit(const FontsEditorState.snackbar(type: SnackBarType.fontDownloadFailed));
          emit(updatedState);
          log('Something goes wrong with downloading image');
        }
      },
    );
  }

  void _handleDownloadFailed(Failure failure) {
    log('Failed download font: $failure');
    state.mapOrNull(
      initial: (state) {
        emit(const FontsEditorState.snackbar(type: SnackBarType.fontDownloadFailed));
        emit(state);
      },
    );
  }

  void _handleDownloadSuccess() {
    log('Success download font');
    state.mapOrNull(
      initial: (state) {
        emit(const FontsEditorState.snackbar(type: SnackBarType.fontDownloadSuccess));
        emit(state);
      },
    );
    // Log A/B testing event
    _togglePositiveCaseAbTest();
  }

  String _applyFontCase(FontCaseType fontCaseType, String text) {
    if (text.isEmpty) {
      return text;
    }
    return switch (fontCaseType) {
      FontCaseType.uppercase => text.toUpperCase(),
      FontCaseType.lowercase => text.toLowerCase(),
      FontCaseType.standard => _capitalizeSentences(text),
    };
  }

  String _capitalizeSentences(String text) => text
      .split(RegExp(r'(?<=\.\s|\n)'))
      .map(
        (sentence) => sentence.trimLeft().isNotEmpty
            ? sentence.trimLeft()[0].toUpperCase() + sentence.trimLeft().substring(1).toLowerCase()
            : sentence,
      )
      .join();

  void _onKeyboardClose() {
    state.mapOrNull(
      initial: (state) {
        _analytics.onKeyboardClose();
        emit(
          state.copyWith(
            appBarState: state.appBarState.copyWith(type: AppBarStateType.idle),
            dragAndDropEnabled: true,
          ),
        );
      },
    );
  }

  void _onKeyboardOpened() {
    state.mapOrNull(
      initial: (state) {
        _analytics.onKeyboardOpened();
        emit(
          state.copyWith(
            appBarState: state.appBarState.copyWith(type: AppBarStateType.writing),
            dragAndDropEnabled: false,
            bottomBarSelectedIndex: 0,
          ),
        );
      },
    );
  }

  FontsEditorInitialState _getInitialState(CustomFontModel font, {bool deletedText = false}) =>
      FontsEditorState.initial(
        initialFont: font,
        dragAndDropEnabled: false,
        appBarState: AppBarState(
          type: AppBarStateType.writing,
          forwardButtonActive: _forwardHistoryButtonActive,
          backButtonActive: _backHistoryButtonActive,
        ),
        textField: TextFieldState(
          currentFontModel: font,
          shadow: ShadowState.initial(),
          deletedText: deletedText,
        ),
        fontsSupportType: FontsSupportType.all,
        showFavoriteInFontsSelectTab: false,
      ) as FontsEditorInitialState;

  void _historyRestore(FontsEditorInitialState state) {
    // Take next history state
    final historyState = _stateHistoryList[_currentIndex];

    // Check is need to restore text if it was full deleted, or if need to delete text if next is full delete state
    final text = historyState.deletedText || state.textField.deletedText ? historyState.text : state.textField.text;

    // If history state has formatter need to format current text and set it
    final formattedText = _getUpdatedTextDependsOnHistory(
      state.textField.fontCaseType,
      historyState.fontCaseType,
      text,
    );

    emit(
      state.copyWith(
        textField: historyState.copyWith(text: formattedText),
        appBarState: _appBarWithUpdatedHistoryButtonState(state.appBarState),
      ),
    );
  }

  void _saveStateToHistory(TextFieldState textFieldState) {
    state.mapOrNull(
      initial: (state) {
        if (_stateHistoryList.isEmpty || _stateHistoryList[_currentIndex] != state.textField) {
          // If not current element not last need to delete all history that more forward
          if (_currentIndex < _stateHistoryList.length - 1) {
            _stateHistoryList.removeRange(_currentIndex + 1, _stateHistoryList.length);
          }
          _stateHistoryList.add(textFieldState);
          _currentIndex = _stateHistoryList.length - 1;

          emit(state.copyWith(appBarState: _appBarWithUpdatedHistoryButtonState(state.appBarState)));
        }
      },
    );
  }

  AppBarState _appBarWithUpdatedHistoryButtonState(AppBarState appBarState) => appBarState.copyWith(
        backButtonActive: _backHistoryButtonActive,
        forwardButtonActive: _forwardHistoryButtonActive,
      );

  bool get _forwardHistoryButtonActive => _currentIndex != -1 && _currentIndex < _stateHistoryList.length - 1;

  bool get _backHistoryButtonActive => _currentIndex > 0;

  String _getUpdatedTextDependsOnHistory(FontCaseType currentCaseType, FontCaseType newCaseType, String text) {
    if (currentCaseType != newCaseType) {
      return _applyFontCase(newCaseType, text);
    } else {
      return text;
    }
  }

  Future<void> _togglePositiveCaseAbTest() async {
    _abTestingService.toggleFontsPositiveCase().then((isPurchaseShowing) {
      if (!isPurchaseShowing) {
        _abTestingService.toggleAllowNotificationsCase();
      }
    });
  }

  /// Find the category name for a given font family
  String _findFontCategory(String fontFamily) {
    final categoryList = _customFontsProvider.categoryList;

    // Find the category that contains the font family
    for (final category in categoryList) {
      final fontExists = category.fontsList.any((font) => font.fontFamily == fontFamily);
      if (fontExists) {
        // Return the category type name
        return category.type.message;
      }
    }

    // Return a default value if the font family is not found in any category
    return 'Unknown';
  }

  @override
  Future<void> close() {
    _keyboardSubscription.cancel();
    return super.close();
  }
}
