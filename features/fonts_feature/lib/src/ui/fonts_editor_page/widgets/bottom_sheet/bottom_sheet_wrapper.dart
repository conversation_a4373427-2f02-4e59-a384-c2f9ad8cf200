part of '../../fonts_editor_page.dart';

class _BottomSheetWrapper extends StatelessWidget {
  final FontsEditorInitialState state;

  const _BottomSheetWrapper({required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<FontsEditorCubit>();
    final index = state.bottomBarSelectedIndex;
    final textField = state.textField;

    return AnimatedSize(
      duration: const Duration(milliseconds: 100),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: context.colors.grayCold9,
          borderRadius: _shouldHasRoundTopBorder(index)
              ? const BorderRadius.vertical(top: Radius.circular(16))
              : BorderRadius.zero,
        ),
        child: switch (index) {
          0 => SelectFonts(
              currentFont: textField.currentFontModel,
              onFontSelected: cubit.onFontSelected,
              fontsSupportType: state.fontsSupportType,
              shouldOpenFavorites: state.showFavoriteInFontsSelectTab,
            ),
          1 => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: ColorPickerTab(selectedColor: textField.textColor, onColorSelected: cubit.onSelectColor),
            ),
          2 => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: SelectAlignmentTab(selectedAlign: textField.textAlign, onAlignSelected: cubit.onSelectAlign),
            ),
          3 => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: FontCaseSelectionTab(
                fontCaseType: textField.fontCaseType,
                onSelectedCaseType: cubit.onSelectFontCaseType,
              ),
            ),
          4 => SelectSpacingTab(
              onLetterSpacingChanged: cubit.onLetterSpacingChanged,
              onLineSpacingChanged: cubit.onLineSpacingChanged,
              onLetterSpacingChangeEnd: cubit.onLetterSpacingChangeEnd,
              onLineSpacingChangeEnd: cubit.onLineSpacingChangeEnd,
              initialLetterSpacing: textField.letterSpacing,
              initialLineSpacing: textField.lineHeight,
            ),
          5 => SelectShadowTab(
              onXChanged: cubit.onShadowXChanged,
              onYChanged: cubit.onShadowYChanged,
              onIntensityChanged: cubit.onShadowIntensityChanged,
              onBlurChanged: cubit.onShadowBlurChanged,
              onColorSelected: cubit.onShadowColorSelected,
              onXChangeEnd: cubit.onShadowXChangeEnd,
              onYChangeEnd: cubit.onShadowYChangeEnd,
              onIntensityChangeEnd: cubit.onShadowIntensityChangeEnd,
              onBlurChangeEnd: cubit.onShadowBlurChangeEnd,
              onResetClick: cubit.onShadowResetClick,
              x: textField.shadow.x,
              y: textField.shadow.y,
              intensity: textField.shadow.intensity,
              blur: textField.shadow.blur,
              color: textField.shadow.color,
            ),
          6 => SelectBlurTab(
              initialBlur: textField.blur,
              onBlurChanged: cubit.onBlurChanged,
              onBlurChangeEnd: cubit.onBlurChangeEnd,
              onResetClick: cubit.onBlurResetClick,
            ),
          _ => const SizedBox(height: 0, width: double.infinity),
        },
      ),
    );
  }

  bool _shouldHasRoundTopBorder(int? index) => switch (state.bottomBarSelectedIndex) {
        0 => true,
        1 => false,
        2 => false,
        3 => false,
        4 => true,
        5 => true,
        _ => false,
      };
}
